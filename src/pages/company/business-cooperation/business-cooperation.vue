<script setup>
// This page is static and does not require any script logic.
</script>

<template>
  <view class="div_04_6">
    <view class="rectangle_450_1">
      <view class="group_1467">
        <view class="group_1466">
          <view class="flexcontainer_1">
            <text class="text_1">132 1234 1234</text>
            <text class="text_2">李村长</text>
          </view>
          <text class="text_3">陕西 延安</text>
        </view>
        <view class="rectangle_607">
          <image class="group_1446" src="/static/business-cooperation.svg" mode="aspectFit" />
        </view>
      </view>
      <image class="wechatimg4426" src="https://placehold.co/590x588" mode="aspectFit" />
      <text class="text_4">保存上面的二维码，微信扫一扫添加好友</text>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.div_04_6 {
  position: relative;
  width: 100%;
  height: auto;
  overflow: hidden;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  isolation: isolate;
  min-height: 100vh;
}

.rectangle_450_1 {
  position: relative;
  width: 100%;
  height: 1464rpx;
  background-color: #ffffff;
  margin-left: auto;
  margin-right: auto;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  isolation: isolate;
  z-index: 0;
  margin-top: 20rpx;
}

.group_1467 {
  position: relative;
  width: 462rpx;
  height: 104rpx;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  isolation: isolate;
  z-index: 1;
  margin-top: 102rpx;
  margin-left: 80rpx;
}

.group_1466 {
  position: relative;
  width: 334rpx;
  height: 84rpx;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  z-index: 1;
  margin-left: 128rpx;
  margin-top: 12rpx;
}

.flexcontainer_1 {
  position: relative;
  display: flex;
  width: 100%;
  height: 38rpx;
  flex-direction: row;
  align-items: flex-start;
  margin-top: 0;
  margin-left: 0;
}

.text_1 {
  position: absolute;
  top: 0;
  left: 112rpx;
  font-size: 32rpx;
  font-weight: 500;
  line-height: 38rpx;
  color: #1a1a1a;
  white-space: pre;
  right: 0;
  width: max-content;
  margin-left: auto;
  margin-right: auto;
}

.text_2 {
  position: absolute;
  top: 0;
  left: 0;
  font-size: 32rpx;
  font-weight: 500;
  line-height: 38rpx;
  color: #1a1a1a;
  white-space: pre;
  right: 238rpx;
  width: max-content;
  margin-left: auto;
  margin-right: auto;
}

.text_3 {
  position: relative;
  font-size: 24rpx;
  font-weight: 400;
  line-height: 28rpx;
  color: #999999;
  white-space: pre;
  width: max-content;
  margin-left: calc(50% - 167rpx);
  margin-right: auto;
  margin-top: 18rpx;
}

.rectangle_607 {
  position: absolute;
  width: 104rpx;
  height: 104rpx;
  top: 0;
  left: 0;
  border-radius: 12rpx;
  background-color: #ffffff;
  right: 358rpx;
  margin-left: auto;
  margin-right: auto;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  z-index: 0;
}

.group_1446 {
  width: 70rpx;
  height: 77rpx;
  position: relative;
  margin-top: 11.5rpx;
  margin-left: auto;
  margin-right: auto;
}

.wechatimg4426 {
  width: 590rpx;
  height: 588rpx;
  object-fit: cover;
  z-index: 2;
  position: relative;
  margin-top: 35rpx;
  margin-left: auto;
  margin-right: auto;
}

.text_4 {
  position: absolute;
  top: 893rpx;
  left: 0;
  font-size: 24rpx;
  font-weight: 400;
  line-height: 28rpx;
  color: #999999;
  white-space: pre;
  right: 0;
  width: max-content;
  margin-left: auto;
  margin-right: auto;
  z-index: 0;
}
</style>
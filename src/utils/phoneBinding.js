import { useUserStore } from '@/store/user'

/**
 * 检查用户是否已绑定手机号
 * @returns {Promise<boolean>} 是否已绑定手机号
 */
export async function isPhoneBound() {
  const userStore = useUserStore()

  // 如果用户信息不存在，先请求用户信息
  if (!userStore.userInfo) {
    await userStore.refreshUserInfo()
  }

  // 检查用户信息是否存在且手机号不为空
  return !!(userStore.userInfo && userStore.userInfo.phonenumber)
}

/**
 * 检查手机号绑定状态，如果未绑定则跳转到绑定页面
 * @param {string} fromPage - 来源页面，用于绑定成功后返回
 * @returns {Promise<boolean>} 是否已绑定（true表示已绑定，false表示未绑定并已跳转）
 */
export async function checkPhoneBindingAndRedirect(fromPage = '') {
  if (await isPhoneBound()) {
    return true
  }

  // 未绑定手机号，重定向到绑定页面（用户无法返回）
  const url = fromPage ? `/pages/auth/bind-phone/bind-phone?from=${encodeURIComponent(fromPage)}` : '/pages/auth/bind-phone/bind-phone'

  uni.redirectTo({
    url: url
  })

  return false
}